{"Version": 1, "WorkspaceRootPath": "D:\\vscode_programs\\wenet-3.1.0\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\vscode_programs\\wenet-3.1.0\\wenet-3.1.0\\runtime\\libtorch\\CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:wenet-3.1.0\\runtime\\libtorch\\CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "CMakeLists.txt", "DocumentMoniker": "D:\\vscode_programs\\wenet-3.1.0\\wenet-3.1.0\\runtime\\libtorch\\CMakeLists.txt", "RelativeDocumentMoniker": "wenet-3.1.0\\runtime\\libtorch\\CMakeLists.txt", "ToolTip": "D:\\vscode_programs\\wenet-3.1.0\\wenet-3.1.0\\runtime\\libtorch\\CMakeLists.txt", "RelativeToolTip": "wenet-3.1.0\\runtime\\libtorch\\CMakeLists.txt", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-03-25T08:34:37.914Z", "EditorCaption": ""}]}]}]}