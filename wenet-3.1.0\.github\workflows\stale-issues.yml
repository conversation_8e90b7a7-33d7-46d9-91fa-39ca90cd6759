name: Close Stale Issues
on:
  schedule:
    - cron: '0 0 * * *'

jobs:
  close-stale-issues:
    if: github.repository == 'wenet-e2e/wenet'
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
      - name: Check for Stale Issues
        uses: actions/stale@v5

      - name: Close Stale Issues
        uses: actions/stale@v5
        with:
          stale-issue-message: 'This issue has been automatically closed due to inactivity.'
          close-issue-message: "This issue was closed because it has been inactive for 7 days
            since being marked as stale. Please reopen if you'd like to work on this further."
          days-before-stale: 60
          days-before-close: 7
          stale-issue-label: stale
          repo-token: ${{ secrets.GITHUB_TOKEN }}
